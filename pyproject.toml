[project]
name = "open-webui"
description = "Open WebUI"
authors = [
    { name = "<PERSON>", email = "<EMAIL>" }
]
license = { file = "LICENSE" }
dependencies = [
    "fastapi==0.111.0",
    "uvicorn[standard]==0.30.6",
    "pydantic==2.9.2",
    "python-multipart==0.0.18",
    "python-socketio==5.11.3",
    "python-jose==3.4.0",
    "passlib[bcrypt]==1.7.4",
    "requests==2.32.3",
    "aiohttp==3.11.8",
    "async-timeout",
    "aiocache",
    "aiofiles",
    "sqlalchemy==2.0.32",
    "alembic==1.14.0",
    "peewee==3.17.6",
    "peewee-migrate==1.12.2",
    "psycopg2-binary==2.9.9",
    "pgvector==0.3.5",
    "PyMySQL==1.1.1",
    "bcrypt==4.2.0",
    "pymongo",
    "redis",
    "boto3==1.35.53",
    "argon2-cffi==23.1.0",
    "APScheduler==3.10.4",
    "openai",
    "anthropic",
    "google-generativeai==0.7.2",
    "tiktoken",
    "langchain==0.3.7",
    "langchain-community==0.3.7",
    "langchain-chroma==0.1.4",
    "fake-useragent==1.5.1",
    "chromadb==0.5.15",
    "pymilvus==2.5.0",
    "qdrant-client~=1.12.0",
    "opensearch-py==2.7.1",
    "sentence-transformers==3.3.1",
    "colbert-ai==0.2.21",
    "einops==0.8.0",
    "ftfy==6.2.3",
    "pypdf==4.3.1",
    "fpdf2==2.7.9",
    "pymdown-extensions==10.11.2",
    "docx2txt==0.8",
    "python-pptx==1.0.0",
    "unstructured==0.15.9",
    "nltk==3.9.1",
    "Markdown==3.7",
    "pypandoc==1.13",
    "pandas==2.2.3",
    "openpyxl==3.1.5",
    "pyxlsb==1.0.10",
    "xlrd==2.0.1",
    "validators==0.33.0",
    "psutil",
    "sentencepiece",
    "soundfile==0.12.1",
    "opencv-python-headless==*********",
    "rapidocr-onnxruntime==1.3.24",
    "rank-bm25==0.2.2",
    "faster-whisper==1.0.3",
    "PyJWT[crypto]==2.9.0",
    "authlib==1.3.2",
    "black==24.8.0",
    "langfuse==2.44.0",
    "youtube-transcript-api==0.6.3",
    "pytube==15.0.0",
    "extract_msg",
    "pydub",
    "duckduckgo-search~=6.3.5",
    "docker~=7.1.0",
    "pytest~=8.3.2",
    "pytest-docker~=3.1.1",
    "googleapis-common-protos==1.63.2",
    "ldap3==2.9.1",
    "structlog>=25.4.0",
    "rich>=13.7.1",
    "ddtrace>=3.9.4",
]
readme = "README.md"
requires-python = ">= 3.11, < 3.13.0a1"
dynamic = ["version"]
classifiers = [
    "Development Status :: 4 - Beta",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Communications :: Chat",
    "Topic :: Multimedia",
]

[project.scripts]
open-webui = "open_webui:app"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.rye]
managed = true
dev-dependencies = []

[tool.hatch.metadata]
allow-direct-references = true

[tool.hatch.version]
path = "package.json"
pattern = '"version":\s*"(?P<version>[^"]+)"'

[tool.hatch.build.hooks.custom]  # keep this for reading hooks from `hatch_build.py`

[tool.hatch.build.targets.wheel]
sources = ["backend"]
exclude = [
    ".dockerignore",
    ".gitignore",
    ".webui_secret_key",
    "dev.sh",
    "requirements.txt",
    "start.sh",
    "start_windows.bat",
    "webui.db",
    "chroma.sqlite3",
]
force-include = { "CHANGELOG.md" = "open_webui/CHANGELOG.md", build = "open_webui/frontend" }
