#!/usr/bin/env python3
"""
Test script to verify the chat title bug fix.
This tests the error detection and fallback title generation functions.
"""

# Standalone implementations for testing (copied from middleware.py)
def is_error_message(content: str) -> bool:
    """
    Check if the content is an error message from pipelines.
    Returns True if the content appears to be an error message.
    """
    if not content or not isinstance(content, str):
        return False

    content_lower = content.lower().strip()

    # Check for common error message patterns
    error_patterns = [
        "##oops!",
        "## oops!",
        "gsa chat is having some trouble",
        "gsa chat has hit a service limit",
        "please try another model",
        "wait a minute and try again"
    ]

    return any(pattern in content_lower for pattern in error_patterns)


def generate_fallback_title(messages: list[dict]) -> str:
    """
    Generate a fallback title when the model returns an error message.
    Uses the user's first message, truncated to a reasonable length.
    """
    if not messages:
        return "New Chat"

    # Find the first user message
    user_message = None
    for msg in messages:
        if msg.get("role") == "user" and msg.get("content"):
            user_message = msg.get("content", "").strip()
            break

    if not user_message:
        return "New Chat"

    # Truncate to reasonable title length (50 characters)
    if len(user_message) > 50:
        user_message = user_message[:47] + "..."

    return user_message


def test_error_detection():
    """Test the is_error_message function with various inputs."""
    print("Testing error message detection...")
    
    # Test cases that should be detected as errors
    error_cases = [
        "## Oops! 🤖💔\n\n ### GSA Chat is having some trouble.\n\nPlease try another model _or_ wait a minute and try again.",
        "##Oops! 🤖💔\n\n ### GSA Chat has hit a service limit.\n\nPlease try another model _or_ wait a minute and try again.",
        "## Oops! GSA Chat is having some trouble. Please try another model or wait a minute and try again.",
        "Something went wrong. GSA Chat is having some trouble.",
        "Error: GSA Chat has hit a service limit. Please try another model.",
    ]
    
    # Test cases that should NOT be detected as errors
    normal_cases = [
        "How to cook pasta",
        "📚 Python Programming Guide",
        "🎮 Video Game Development",
        "Colors of the Rainbow",
        "New Chat",
        "",
        None,
    ]
    
    print("Testing error cases (should return True):")
    for i, case in enumerate(error_cases, 1):
        result = is_error_message(case)
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {i}. {status} - {case[:50]}{'...' if case and len(case) > 50 else ''}")
    
    print("\nTesting normal cases (should return False):")
    for i, case in enumerate(normal_cases, 1):
        result = is_error_message(case)
        status = "✅ PASS" if not result else "❌ FAIL"
        case_display = str(case)[:50] if case else str(case)
        print(f"  {i}. {status} - {case_display}")


def test_fallback_title_generation():
    """Test the generate_fallback_title function."""
    print("\n" + "="*60)
    print("Testing fallback title generation...")
    
    test_cases = [
        # Normal case with user message
        {
            "messages": [
                {"role": "user", "content": "How do I cook pasta?"},
                {"role": "assistant", "content": "To cook pasta..."}
            ],
            "expected": "How do I cook pasta?"
        },
        # Long user message (should be truncated)
        {
            "messages": [
                {"role": "user", "content": "This is a very long message that should be truncated because it exceeds the 50 character limit for chat titles"},
                {"role": "assistant", "content": "Here's the answer..."}
            ],
            "expected": "This is a very long message that should be trun..."
        },
        # System message first, then user message
        {
            "messages": [
                {"role": "system", "content": "You are a helpful assistant"},
                {"role": "user", "content": "What is Python?"},
                {"role": "assistant", "content": "Python is..."}
            ],
            "expected": "What is Python?"
        },
        # Empty messages
        {
            "messages": [],
            "expected": "New Chat"
        },
        # No user messages
        {
            "messages": [
                {"role": "system", "content": "System message"},
                {"role": "assistant", "content": "Assistant message"}
            ],
            "expected": "New Chat"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        result = generate_fallback_title(test_case["messages"])
        expected = test_case["expected"]
        status = "✅ PASS" if result == expected else "❌ FAIL"
        print(f"  {i}. {status} - Expected: '{expected}', Got: '{result}'")


if __name__ == "__main__":
    print("Chat Title Bug Fix - Test Suite")
    print("="*60)
    
    try:
        test_error_detection()
        test_fallback_title_generation()
        print("\n" + "="*60)
        print("Test suite completed!")
    except Exception as e:
        print(f"Error running tests: {e}")
        import traceback
        traceback.print_exc()
